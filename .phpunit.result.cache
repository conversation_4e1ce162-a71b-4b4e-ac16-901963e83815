{"version": 1, "defects": {"backend\\controllers\\ProjectsControllerTest::testActionFileOptionsEncode": 8, "common\\chip\\alientech\\services\\AlientechProjectServiceTest::testProcessSuccessOperationDecode": 5, "common\\chip\\project\\services\\CreateProjectServiceTest::test__construct": 8, "common\\chip\\project\\services\\CreateProjectServiceTest::testRun": 5, "common\\chip\\project\\services\\CreateProjectServiceTest::testGetModelForm": 5, "PHPUnit_ProjectCreationServiceTest::testCreateProjectSuccess": 8, "PHPUnit_ProjectCreationServiceTest::testCreateProjectError": 8, "PHPUnit_ProjectCreationServiceTest::testCreateProjectNull": 8, "PHPUnit_ProjectCreationServiceTest::testGetModelFormForRegularUser": 8, "PHPUnit_ProjectCreationServiceTest::testGetModelFormForDealer": 8, "PHPUnit_ProjectCreationServiceTest::testGetModelFormForMsDealer": 8, "ProjectCreationOrchestratorDatabaseTest::testGetModelForm": 8, "ProjectCreationOrchestratorDatabaseTest::testCreateMinimalProject": 1, "ProjectCreationOrchestratorDatabaseTest::testCreateProjectWithOptions": 1, "ProjectCreationOrchestratorIntegrationTest::testCreateProjectIntegration": 8, "ProjectCreationOrchestratorIntegrationTest::testGetModelForm": 8, "ProjectCreationOrchestratorIntegrationTest::testCalculateUserTools": 8, "ProjectCreationOrchestratorIntegrationTest::testCreateProjectWithError": 8, "ProjectCreationOrchestratorIntegrationTest::testCreateProjectWithEmptyOptionsAndFiles": 8, "ProjectCreationOrchestratorDatabaseTest::testCalculateUserTools": 8, "ProjectCreationOrchestratorDatabaseTest::testCreateProjectWithFiles": 1, "common\\chip\\notification\\tests\\DefaultChannelSelectorTest::testSelectChannelsWithUserPreferences": 8, "common\\chip\\notification\\tests\\DefaultChannelSelectorTest::testSelectChannelsWithDefaultPreferences": 8, "common\\chip\\notification\\tests\\DefaultChannelSelectorTest::testSelectChannelsWithConfigDefaults": 8, "common\\chip\\notification\\tests\\DefaultChannelSelectorTest::testSelectChannelsFiltersInactiveChannels": 8, "common\\chip\\notification\\tests\\DefaultChannelSelectorTest::testSelectChannelsFiltersUnsupportedPriorities": 8, "common\\chip\\notification\\tests\\DefaultChannelSelectorTest::testSelectChannelsHighPriorityOverridesSupport": 8, "common\\chip\\notification\\tests\\DefaultChannelSelectorTest::testGetAllChannelsReturnsAllRegisteredChannels": 8, "common\\chip\\notification\\tests\\DefaultChannelSelectorTest::testGetChannelsForRoleReturnsFilteredChannels": 8, "common\\chip\\notification\\tests\\EventBusTest::testSubscribeAndPublish": 8, "common\\chip\\notification\\tests\\EventBusTest::testUnsubscribe": 8, "common\\chip\\notification\\tests\\EventBusTest::testMultipleListeners": 8, "common\\chip\\notification\\tests\\EventBusTest::testWildcardListener": 8, "common\\chip\\notification\\tests\\EventBusTest::testEventLog": 8, "common\\chip\\notification\\tests\\EventBusTest::testClearEventLog": 8, "common\\chip\\notification\\tests\\LegacyEventEmitterTest::testEmitProjectCreatedEvent": 8, "common\\chip\\notification\\tests\\LegacyEventEmitterTest::testEmitProjectUpdatedEvent": 8, "common\\chip\\notification\\tests\\LegacyEventEmitterTest::testEmitProjectStatusChangedEvent": 8, "common\\chip\\notification\\tests\\LegacyEventEmitterTest::testEmitProjectClosedEvent": 8, "common\\chip\\notification\\tests\\LegacyEventEmitterTest::testEmitProjectNoteAddedEvent": 8, "common\\chip\\notification\\tests\\LegacyEventEmitterTest::testEmitFileUploadedEvent": 8, "common\\chip\\notification\\tests\\LegacyEventEmitterTest::testEmitFileUnpackedEvent": 8, "common\\chip\\notification\\tests\\LegacyEventEmitterTest::testEmitFilePackedEvent": 8, "common\\chip\\notification\\tests\\LegacyEventEmitterTest::testEmitFileProcessedEvent": 8, "common\\chip\\notification\\tests\\LegacyEventEmitterTest::testEmitFileProcessingErrorEvent": 8, "common\\chip\\notification\\tests\\LegacyEventEmitterTest::testInvalidActionReturnsNull": 8, "common\\chip\\notification\\tests\\LegacyEventEmitterTest::testGetEventBus": 8, "common\\chip\\notification\\tests\\LegacyNotificationAdapterTest::testAdaptAndSendTelegram": 8, "common\\chip\\notification\\tests\\LegacyNotificationAdapterTest::testAdaptAndSendEmail": 8, "common\\chip\\notification\\tests\\LegacyNotificationAdapterTest::testAdaptAndSendUi": 8, "common\\chip\\notification\\tests\\LegacyNotificationAdapterTest::testAdaptAndSendWithError": 8, "common\\chip\\notification\\tests\\LegacyNotificationAdapterTest::testAdaptAndSendWithUnknownChannel": 8, "common\\chip\\notification\\tests\\LegacyNotificationAdapterTest::testGetLegacyService": 8, "common\\chip\\notification\\tests\\NotificationABTestTest::testCompare": 8, "common\\chip\\notification\\tests\\NotificationABTestTest::testCaptureNewSystemResult": 8, "common\\chip\\notification\\tests\\NotificationABTestTest::testExtractDataFromEvent": 8, "common\\chip\\notification\\tests\\NotificationABTestTest::testMapEventTypeToAction": 8, "common\\chip\\notification\\tests\\NotificationFacadeTest::testNotifyProjectCreated": 8, "common\\chip\\notification\\tests\\NotificationFacadeTest::testNotifyProjectCreatedWithLegacySystem": 8, "common\\chip\\notification\\tests\\NotificationFacadeTest::testNotifyNoteAdded": 8, "common\\chip\\notification\\tests\\NotificationFacadeTest::testNotifyFileUploaded": 8, "common\\chip\\notification\\tests\\NotificationFacadeTest::testIsChannelEnabled": 8, "common\\chip\\notification\\tests\\NotificationFacadeTest::testIsFeatureEnabled": 8, "common\\chip\\notification\\tests\\NotificationFacadeTest::testGetters": 8, "common\\chip\\notification\\tests\\NotificationFactoryTest::testCreateFromEvent": 8, "common\\chip\\notification\\tests\\NotificationFactoryTest::testRegisterTemplate": 8, "common\\chip\\notification\\tests\\NotificationFactoryTest::testFindTemplateForEvent": 8, "common\\chip\\notification\\tests\\NotificationFactoryTest::testGetAllTemplates": 8, "common\\chip\\notification\\tests\\NotificationFactoryTest::testCreateFromEventWithoutTemplate": 8, "common\\chip\\notification\\tests\\NotificationFactoryTest::testGetTemplateEngine": 8, "common\\chip\\notification\\tests\\NotificationMonitoringServiceTest::testLogNotificationAttempt": 8, "common\\chip\\notification\\tests\\NotificationMonitoringServiceTest::testLogNotificationAttemptFailure": 8, "common\\chip\\notification\\tests\\NotificationMonitoringServiceTest::testLogNotificationAttemptWithNewNotification": 8, "common\\chip\\notification\\tests\\NotificationMonitoringServiceTest::testLogNotificationAttemptWithRecipientWithoutId": 8, "common\\chip\\notification\\tests\\NotificationMonitoringServiceTest::testGetSuccessRate": 8, "common\\chip\\notification\\tests\\NotificationMonitoringServiceTest::testGetSuccessRateWithFilter": 8, "common\\chip\\notification\\tests\\NotificationMonitoringServiceTest::testGetChannelUsage": 8, "common\\chip\\notification\\tests\\NotificationMonitoringServiceTest::testGetEventTypeStats": 8, "common\\chip\\notification\\tests\\NotificationPreProcessorTest::testProcessAddsCreatedAtIfMissing": 8, "common\\chip\\notification\\tests\\NotificationPreProcessorTest::testProcessAddsLinksForProjectEvents": 8, "common\\chip\\notification\\tests\\NotificationPreProcessorTest::testProcessAddsLinksForFileEvents": 8, "common\\chip\\notification\\tests\\NotificationPreProcessorTest::testProcessProjectCreatedNotification": 8, "common\\chip\\notification\\tests\\NotificationPreProcessorTest::testProcessFileUploadedNotification": 8, "common\\chip\\notification\\tests\\NotificationPreProcessorTest::testFormatFileSize": 8, "common\\chip\\notification\\tests\\NotificationPreProcessorTest::testGetFileTypeIcon": 8, "common\\chip\\notification\\tests\\NotificationPreProcessorTest::testGetProcessMethodName": 8, "common\\chip\\notification\\tests\\NotificationPreProcessorTest::testProcessHandlesUnknownEventTypes": 8, "common\\chip\\notification\\tests\\NotificationPreProcessorTest::testProcessPreservesOriginalData": 8, "common\\chip\\notification\\tests\\NotificationPreProcessorTest::testProcessWithExistingCreatedAt": 8, "common\\chip\\notification\\tests\\NotificationRouterTest::testRouteNotification": 8, "common\\chip\\notification\\tests\\NotificationRouterTest::testRouteWithCustomHandler": 8, "common\\chip\\notification\\tests\\NotificationRouterTest::testGetHandlerReturnsCorrectHandler": 8, "common\\chip\\notification\\tests\\NotificationRouterTest::testGettersReturnCorrectInstances": 8, "common\\chip\\notification\\tests\\NotificationServiceTest::testProcessEvent": 8, "common\\chip\\notification\\tests\\NotificationServiceTest::testProcessEventWithNullNotificationId": 8, "common\\chip\\notification\\tests\\NotificationServiceTest::testClearProcessedLog": 8, "common\\chip\\notification\\tests\\NotificationServiceTest::testGetFactory": 8, "common\\chip\\notification\\tests\\NotificationServiceTest::testGetRouter": 8, "common\\chip\\notification\\tests\\NotificationServiceTest::testLogSizeLimit": 8, "common\\chip\\notification\\tests\\TemplateEngineTest::testRender": 8, "common\\chip\\notification\\tests\\TemplateEngineTest::testRenderWithNestedData": 8, "common\\chip\\notification\\tests\\TemplateEngineTest::testRenderWithMissingVariable": 8, "common\\chip\\notification\\tests\\TemplateEngineTest::testRenderWithFunction": 8, "common\\chip\\notification\\tests\\TemplateEngineTest::testRenderWithUnknownFunction": 8, "common\\chip\\notification\\tests\\TemplateEngineTest::testRenderTitle": 8, "common\\chip\\notification\\tests\\TemplateEngineTest::testRenderBody": 8, "common\\chip\\notification\\tests\\TemplateEngineTest::testRegisterFunction": 8, "common\\chip\\notification\\tests\\TemplateEngineTest::testGetFunctions": 8, "common\\chip\\notification\\tests\\TemplateEngineTest::testDefaultFunctionDate": 8, "common\\chip\\notification\\tests\\TemplateEngineTest::testDefaultFunctionTruncate": 8, "common\\chip\\notification\\tests\\TemplateEngineTest::testDefaultFunctionUpper": 8, "common\\chip\\notification\\tests\\TemplateEngineTest::testDefaultFunctionLower": 8, "common\\chip\\notification\\tests\\TemplateEngineTest::testDefaultFunctionJoin": 8, "tests\\PHPUnit\\notification\\NotificationFacadeTest::testNotifyProjectCreated": 8, "tests\\integration\\AsyncEventDispatchTest::testAsyncEventDispatch": 8, "tests\\integration\\AsyncEventDispatchTest::testAsyncJobExecution": 7, "tests\\integration\\AsyncEventDispatchTest::testAsyncJobErrorHandling": 7, "tests\\integration\\RetryMechanismTest::testRetryOnError": 7, "tests\\integration\\RetryMechanismTest::testExponentialBackoff": 8, "tests\\integration\\Kess3DecodingTest::testCreateDecodeCommand": 1, "tests\\integration\\Kess3DecodingTest::testFileValidation": 1, "tests\\integration\\Kess3DecodingTest::testExecuteDecodeCommand": 8, "tests\\integration\\Kess3DecodingTest::testErrorHandling": 1, "tests\\integration\\Kess3DecodingTest::testHealthCheck": 1, "tests\\integration\\Kess3DecodingTest::testBatchExecution": 8, "tests\\unit\\kess3\\application\\CommandHandlerTest::testCreateStartDecodingCommand": 8, "tests\\unit\\kess3\\application\\CommandHandlerTest::testStartDecodingCommandValidation": 8, "tests\\unit\\kess3\\application\\CommandHandlerTest::testCommandSerialization": 8, "tests\\unit\\kess3\\application\\CommandHandlerTest::testStartDecodingHandlerSuccess": 8, "tests\\unit\\kess3\\application\\CommandHandlerTest::testStartDecodingHandlerApiError": 8, "tests\\unit\\kess3\\application\\CommandHandlerTest::testStartDecodingHandlerOperationExists": 8, "tests\\unit\\kess3\\application\\CommandHandlerTest::testCreateProcessDecodingResultCommand": 8, "tests\\unit\\kess3\\application\\CommandHandlerTest::testProcessDecodingResultHandlerSuccess": 8, "tests\\unit\\kess3\\application\\CommandHandlerTest::testProcessDecodingResultHandlerFailure": 8, "tests\\unit\\kess3\\application\\CommandHandlerTest::testProcessDecodingResultHandlerOperationNotFound": 8, "tests\\unit\\kess3\\application\\CommandHandlerTest::testCommandPriorityAndMetadata": 8, "tests\\unit\\kess3\\application\\CommandHandlerTest::testCommandRetryMechanism": 8, "tests\\unit\\kess3\\application\\CommandHandlerTest::testCommandTimeout": 8, "tests\\unit\\kess3\\application\\CommandHandlerTest::testCommandHandlerPerformance": 8, "tests\\unit\\kess3\\application\\CommandHandlerTest::testCommandImmutability": 8, "tests\\unit\\kess3\\application\\CommandHandlerTest::testCommandChaining": 8, "tests\\integration\\kess3\\CompleteDecodingFlowTest::testCompleteSuccessfulDecodingFlow": 8, "tests\\integration\\kess3\\CompleteDecodingFlowTest::testCompleteFailedDecodingFlow": 8, "tests\\integration\\kess3\\CompleteDecodingFlowTest::testOperationTimeoutFlow": 8, "tests\\integration\\kess3\\CompleteDecodingFlowTest::testConcurrentOperationsFlow": 8, "tests\\integration\\kess3\\CompleteDecodingFlowTest::testRecoveryAfterFailureFlow": 8, "tests\\integration\\kess3\\CompleteDecodingFlowTest::testFullCyclePerformance": 8, "tests\\integration\\kess3\\application\\Kess3DecodingFacadeTest::testStartDecoding": 8, "tests\\integration\\kess3\\application\\Kess3DecodingFacadeTest::testStartDecodingWithCallback": 8, "tests\\integration\\kess3\\application\\Kess3DecodingFacadeTest::testProcessDecodingResult": 8, "tests\\integration\\kess3\\application\\Kess3DecodingFacadeTest::testProcessDecodingError": 8, "tests\\integration\\kess3\\application\\Kess3DecodingFacadeTest::testGetProjectStatistics": 8, "tests\\integration\\kess3\\application\\Kess3DecodingFacadeTest::testGetProjectOperations": 8, "tests\\integration\\kess3\\application\\Kess3DecodingFacadeTest::testGetInProgressOperations": 8, "tests\\integration\\kess3\\application\\Kess3DecodingFacadeTest::testCancelExpiredOperations": 8, "tests\\integration\\kess3\\application\\Kess3DecodingFacadeTest::testGetSystemStatistics": 8, "tests\\integration\\kess3\\application\\Kess3DecodingFacadeTest::testHandleInvalidData": 7, "tests\\integration\\kess3\\application\\Kess3DecodingFacadeTest::testDuplicateDecodingRequest": 8, "tests\\integration\\kess3\\application\\Kess3DecodingFacadeTest::testFacadePerformance": 8, "tests\\integration\\kess3\\infrastructure\\DecodingConsoleControllerTest::testStartDecodingCommand": 8, "tests\\integration\\kess3\\infrastructure\\DecodingConsoleControllerTest::testStartDecodingWithCallbackCommand": 8, "tests\\integration\\kess3\\infrastructure\\DecodingConsoleControllerTest::testStatusCommand": 8, "tests\\integration\\kess3\\infrastructure\\DecodingConsoleControllerTest::testStatusCommandNotFound": 8, "tests\\integration\\kess3\\infrastructure\\DecodingConsoleControllerTest::testProjectStatsCommand": 8, "tests\\integration\\kess3\\infrastructure\\DecodingConsoleControllerTest::testCancelExpiredCommand": 8, "tests\\integration\\kess3\\infrastructure\\DecodingConsoleControllerTest::testListActiveCommand": 8, "tests\\integration\\kess3\\infrastructure\\DecodingConsoleControllerTest::testSystemStatsCommand": 8, "tests\\integration\\kess3\\infrastructure\\DecodingConsoleControllerTest::testCleanupCommand": 8, "tests\\integration\\kess3\\infrastructure\\DecodingConsoleControllerTest::testMonitorCommand": 8, "tests\\integration\\kess3\\infrastructure\\DecodingConsoleControllerTest::testRetryFailedCommand": 8, "tests\\integration\\kess3\\infrastructure\\DecodingConsoleControllerTest::testExportCommand": 8, "tests\\integration\\kess3\\infrastructure\\DecodingConsoleControllerTest::testCommandValidation": 8, "tests\\integration\\kess3\\infrastructure\\DecodingConsoleControllerTest::testInteractiveMode": 8, "tests\\integration\\kess3\\infrastructure\\DecodingConsoleControllerTest::testConsoleCommandsPerformance": 8, "tests\\integration\\kess3\\infrastructure\\DecodingConsoleControllerTest::testConsoleErrorHandling": 8, "tests\\integration\\kess3\\infrastructure\\DecodingConsoleControllerTest::testHelpCommand": 8, "tests\\integration\\kess3\\infrastructure\\DecodingEventHandlerTest::testHandleDecodingStartedEvent": 8, "tests\\integration\\kess3\\infrastructure\\DecodingEventHandlerTest::testHandleDecodingCompletedEvent": 8, "tests\\integration\\kess3\\infrastructure\\DecodingEventHandlerTest::testHandleDecodingFailedEvent": 8, "tests\\integration\\kess3\\infrastructure\\DecodingEventHandlerTest::testDownloadDecodedFiles": 8, "tests\\integration\\kess3\\infrastructure\\DecodingEventHandlerTest::testHandleUnknownEvent": 8, "tests\\integration\\kess3\\infrastructure\\DecodingEventHandlerTest::testHandleMultipleEvents": 8, "tests\\integration\\kess3\\infrastructure\\DecodingEventHandlerTest::testProjectNoteFormatting": 8, "tests\\integration\\kess3\\infrastructure\\DecodingEventHandlerTest::testEventBusIntegration": 8, "tests\\integration\\kess3\\infrastructure\\DecodingEventHandlerTest::testErrorHandling": 8, "tests\\integration\\kess3\\infrastructure\\DecodingEventHandlerTest::testEventHandlerPerformance": 8, "tests\\integration\\kess3\\infrastructure\\DecodingEventHandlerTest::testEventIdempotency": 8, "tests\\integration\\kess3\\infrastructure\\DecodingOperationRepositoryTest::testSaveOperation": 8, "tests\\integration\\kess3\\infrastructure\\DecodingOperationRepositoryTest::testFindById": 8, "tests\\integration\\kess3\\infrastructure\\DecodingOperationRepositoryTest::testFindByIdNotFound": 8, "tests\\integration\\kess3\\infrastructure\\DecodingOperationRepositoryTest::testFindByProjectId": 8, "tests\\integration\\kess3\\infrastructure\\DecodingOperationRepositoryTest::testFindByStatus": 8, "tests\\integration\\kess3\\infrastructure\\DecodingOperationRepositoryTest::testFindByExternalOperationId": 8, "tests\\integration\\kess3\\infrastructure\\DecodingOperationRepositoryTest::testFindExpiredOperations": 8, "tests\\integration\\kess3\\infrastructure\\DecodingOperationRepositoryTest::testDeleteOperation": 8, "tests\\integration\\kess3\\infrastructure\\DecodingOperationRepositoryTest::testUpdateOperation": 8, "tests\\integration\\kess3\\infrastructure\\DecodingOperationRepositoryTest::testGetOperationStatistics": 8, "tests\\integration\\kess3\\infrastructure\\DecodingOperationRepositoryTest::testPagination": 8, "tests\\integration\\kess3\\infrastructure\\DecodingOperationRepositoryTest::testSorting": 8, "tests\\integration\\kess3\\infrastructure\\DecodingOperationRepositoryTest::testFiltering": 8, "tests\\integration\\kess3\\infrastructure\\DecodingOperationRepositoryTest::testRepositoryPerformance": 8, "tests\\integration\\kess3\\interfaces\\DecodingControllerTest::testCallbackAction": 8, "tests\\integration\\kess3\\interfaces\\DecodingControllerTest::testCallbackActionWithError": 8, "tests\\integration\\kess3\\interfaces\\DecodingControllerTest::testStatusAction": 8, "tests\\integration\\kess3\\interfaces\\DecodingControllerTest::testStatusActionNotFound": 8, "tests\\integration\\kess3\\interfaces\\DecodingControllerTest::testProjectOperationsAction": 8, "tests\\integration\\kess3\\interfaces\\DecodingControllerTest::testCancelExpiredAction": 8, "tests\\integration\\kess3\\interfaces\\DecodingControllerTest::testStartDecodingAction": 8, "tests\\integration\\kess3\\interfaces\\DecodingControllerTest::testStartDecodingValidation": 8, "tests\\integration\\kess3\\interfaces\\DecodingControllerTest::testActiveOperationsAction": 8, "tests\\integration\\kess3\\interfaces\\DecodingControllerTest::testSystemStatisticsAction": 8, "tests\\integration\\kess3\\interfaces\\DecodingControllerTest::testExceptionHandling": 8, "tests\\integration\\kess3\\interfaces\\DecodingControllerTest::testCorsHeaders": 8, "tests\\integration\\kess3\\interfaces\\DecodingControllerTest::testControllerPerformance": 8, "tests\\integration\\kess3\\EventAndHandlerIntegrationTest::testProjectCreatedEventHandled": 8, "tests\\integration\\kess3\\EventAndHandlerIntegrationTest::testFileUploadedEventHandled": 8, "tests\\integration\\kess3\\EventAndHandlerIntegrationTest::testEncodingStartedEventHandled": 8, "tests\\integration\\kess3\\EventAndHandlerIntegrationTest::testEncodingCompletedEventHandled": 8, "tests\\integration\\kess3\\EventAndHandlerIntegrationTest::testDecodingStartedEventHandled": 8, "tests\\integration\\kess3\\EventAndHandlerIntegrationTest::testDecodingCompletedEventHandled": 8, "tests\\integration\\kess3\\FileDecodingTest::testFullDecodingProcess": 8, "tests\\integration\\kess3\\FileEncodingTest::testFullEncodingProcess": 8, "tests\\integration\\kess3\\NotificationEventHandlerIntegrationTest::testSystemErrorEventHandled": 8, "tests\\integration\\kess3\\NotificationEventHandlerIntegrationTest::testProjectStatusChangedEventHandled": 8, "tests\\integration\\kess3\\NotificationEventHandlerIntegrationTest::testNoteAddedEventHandled": 8, "tests\\integration\\kess3\\ProjectCreationTest::testProjectCreation": 8, "tests\\integration\\kess3\\IntegratedEncodingWithUploadTest::testEncodingProcessFlow": 8, "tests\\integration\\kess3\\IntegratedEncodingWithUploadTest::testErrorHandlingInIntegratedProcess": 8, "tests\\integration\\kess3\\IntegratedEncodingWithUploadTest::testAdapterFactoryWithDifferentServices": 8, "tests\\integration\\kess3\\CompleteEncodingFlowTest::testCompleteEncodingFlowWithCustomAdapter": 8, "tests\\integration\\kess3\\CompleteEncodingFlowTest::testEncodingFacadeIntegration": 8, "tests\\integration\\kess3\\CompleteEncodingFlowTest::testFactoryCreatesAllSupportedAdapters": 8, "tests\\integration\\kess3\\CompleteEncodingFlowTest::testEncodingWithDifferentAdapters": 8, "tests\\unit\\chip\\application\\CreateProjectHandlerTest::testSuccessfulCommandHandling": 8, "tests\\unit\\chip\\application\\CreateProjectHandlerTest::testCommandHandlingWithException": 8, "tests\\unit\\chip\\application\\CreateProjectHandlerTest::testCorrectCommandPassedToUseCase": 8, "tests\\unit\\chip\\application\\CreateProjectUseCaseTest::testSuccessfulProjectCreation": 8, "tests\\unit\\chip\\application\\CreateProjectUseCaseTest::testProjectCreationWithNonExistentUser": 8, "tests\\unit\\chip\\application\\CreateProjectUseCaseTest::testProjectCreationWithDomainServiceError": 8, "tests\\unit\\chip\\application\\CreateProjectUseCaseTest::testProjectCreationWithRepositoryError": 8, "tests\\unit\\chip\\application\\CreateProjectUseCaseTest::testProjectCreationWithMultipleEvents": 8, "tests\\unit\\chip\\application\\CreateProjectUseCaseTest::testProjectCreationWithoutEvents": 8, "tests\\unit\\chip\\application\\UploadFileUseCaseTest::testSuccessfulFileUpload": 8, "tests\\unit\\chip\\application\\UploadFileUseCaseTest::testFileUploadToNonExistentProject": 8, "tests\\unit\\chip\\application\\UploadFileUseCaseTest::testFileUploadWithValidationError": 8, "tests\\unit\\chip\\application\\UploadFileUseCaseTest::testFileUploadWithStorageError": 8, "tests\\unit\\chip\\application\\UploadFileUseCaseTest::testFileUploadWithProjectError": 8, "tests\\unit\\chip\\application\\UploadFileUseCaseTest::testFileUploadWithProjectSaveError": 8, "tests\\unit\\chip\\application\\UploadFileUseCaseTest::testFileUploadWithoutEvents": 8, "tests\\unit\\chip\\domain\\DomainEventsTest::testProjectCreatedEvent": 8, "tests\\unit\\chip\\domain\\DomainEventsTest::testProjectCreatedEventPayload": 8, "tests\\unit\\chip\\domain\\DomainEventsTest::testFileUploadedEvent": 8, "tests\\unit\\chip\\domain\\DomainEventsTest::testFileUploadedEventPayload": 8, "tests\\unit\\chip\\domain\\DomainEventsTest::testEventOccurredAt": 8, "tests\\unit\\chip\\domain\\DomainEventsTest::testEventTimestampUniqueness": 8, "tests\\unit\\chip\\domain\\DomainEventsTest::testEventSerialization": 8, "tests\\unit\\chip\\domain\\DomainEventsTest::testEventImmutability": 8, "tests\\unit\\chip\\domain\\DomainEventsTest::testEventCreationPerformance": 8, "tests\\unit\\chip\\domain\\ProjectDomainServiceTest::testCreateProject": 8, "tests\\unit\\chip\\domain\\ProjectDomainServiceTest::testCanUserAccessProject": 8, "tests\\unit\\chip\\domain\\ProjectDomainServiceTest::testCanProjectBeProcessed": 8, "tests\\unit\\chip\\domain\\ProjectDomainServiceTest::testCalculateProjectPriority": 8, "tests\\unit\\chip\\domain\\ProjectDomainServiceTest::testIsProjectEligibleForDiscount": 8, "tests\\unit\\chip\\domain\\ProjectDomainServiceTest::testValidateProjectCompletion": 8, "tests\\unit\\chip\\domain\\ProjectDomainServiceTest::testCreateProjectWithInvalidData": 8, "tests\\unit\\chip\\domain\\ProjectDomainServiceTest::testCreateProjectWithPricingError": 8, "tests\\unit\\chip\\domain\\ProjectFileTest::testCreateProjectFile": 8, "tests\\unit\\chip\\domain\\ProjectFileTest::testMarkAsProcessing": 8, "tests\\unit\\chip\\domain\\ProjectFileTest::testMarkAsCompleted": 8, "tests\\unit\\chip\\domain\\ProjectFileTest::testMarkAsFailed": 8, "tests\\unit\\chip\\domain\\ProjectFileTest::testCannotTransitionToInvalidStatus": 8, "tests\\unit\\chip\\domain\\ProjectFileTest::testUpdatePath": 8, "tests\\unit\\chip\\domain\\ProjectFileTest::testFileStatusTransitions": 8, "tests\\unit\\chip\\domain\\ProjectFileTest::testFileStatusTransitionsWithError": 8, "tests\\unit\\chip\\domain\\ProjectFileTest::testIsProcessable": 8, "tests\\unit\\chip\\domain\\ProjectFilesCollectionTest::testCreateEmptyCollection": 8, "tests\\unit\\chip\\domain\\ProjectFilesCollectionTest::testAddFile": 8, "tests\\unit\\chip\\domain\\ProjectFilesCollectionTest::testAddMultipleFiles": 8, "tests\\unit\\chip\\domain\\ProjectFilesCollectionTest::testFindById": 8, "tests\\unit\\chip\\domain\\ProjectFilesCollectionTest::testRemoveFile": 8, "tests\\unit\\chip\\domain\\ProjectFilesCollectionTest::testRemoveNonExistentFile": 8, "tests\\unit\\chip\\domain\\ProjectFilesCollectionTest::testIteration": 8, "tests\\unit\\chip\\domain\\ProjectFilesCollectionTest::testToArray": 8, "tests\\unit\\chip\\domain\\ProjectFilesCollectionTest::testReplaceFileWithSameId": 8, "tests\\unit\\chip\\domain\\ProjectFilesCollectionTest::testCount": 8, "tests\\unit\\chip\\domain\\ProjectFilesCollectionTest::testIsEmpty": 8, "tests\\unit\\chip\\domain\\ProjectTest::testCreateProject": 8, "tests\\unit\\chip\\domain\\ProjectTest::testUploadFile": 8, "tests\\unit\\chip\\domain\\ProjectTest::testStartProcessing": 8, "tests\\unit\\chip\\domain\\ProjectTest::testCompleteProject": 8, "tests\\unit\\chip\\domain\\ProjectTest::testCannotUploadFileInWrongStatus": 8, "tests\\unit\\chip\\domain\\ProjectTest::testCannotStartProcessingWithoutFiles": 8, "tests\\unit\\chip\\domain\\ProjectTest::testStatusTransitions": 8, "tests\\unit\\chip\\domain\\ProjectTest::testReleaseEvents": 8, "tests\\unit\\chip\\domain\\ValueObjectsTest::testProjectId": 8, "tests\\unit\\chip\\domain\\ValueObjectsTest::testProjectStatus": 8, "tests\\unit\\chip\\domain\\ValueObjectsTest::testProjectStatusTransitions": 8, "tests\\unit\\chip\\domain\\ValueObjectsTest::testMoney": 8, "tests\\unit\\chip\\domain\\ValueObjectsTest::testCurrency": 8, "tests\\unit\\chip\\domain\\ValueObjectsTest::testFileType": 8, "tests\\unit\\chip\\domain\\ValueObjectsTest::testFileStatus": 8, "tests\\unit\\chip\\domain\\ValueObjectsTest::testFileName": 8, "tests\\unit\\chip\\domain\\ValueObjectsTest::testBrand": 8, "tests\\unit\\chip\\domain\\ValueObjectsTest::testImmutability": 8, "tests\\unit\\chip\\domain\\ValueObjectsTest::testIdUniqueness": 8, "tests\\unit\\chip\\infrastructure\\YiiEventDispatcherTest::testDispatchSingleEvent": 8, "tests\\unit\\chip\\infrastructure\\YiiEventDispatcherTest::testDispatchMultipleEvents": 8, "tests\\unit\\chip\\infrastructure\\YiiEventDispatcherTest::testDispatchEmptyEventArray": 8, "tests\\unit\\chip\\infrastructure\\YiiEventDispatcherTest::testDispatchDifferentEventTypes": 8, "tests\\unit\\chip\\infrastructure\\YiiEventDispatcherTest::testEventDispatcherInstantiation": 8, "tests\\unit\\chip\\infrastructure\\YiiProjectRepositoryTest::testSaveProject": 8, "tests\\unit\\chip\\infrastructure\\YiiProjectRepositoryTest::testFindById": 8, "tests\\unit\\chip\\infrastructure\\YiiProjectRepositoryTest::testFindByClientId": 8, "tests\\unit\\chip\\infrastructure\\YiiProjectRepositoryTest::testFindByStatus": 8, "tests\\unit\\chip\\infrastructure\\YiiProjectRepositoryTest::testFindActiveProjects": 8, "tests\\unit\\chip\\infrastructure\\YiiProjectRepositoryTest::testDeleteProject": 8, "tests\\unit\\chip\\infrastructure\\YiiProjectRepositoryTest::testProjectExists": 8, "tests\\unit\\chip\\infrastructure\\YiiProjectRepositoryTest::testCountProjects": 8, "tests\\unit\\chip\\infrastructure\\YiiProjectRepositoryTest::testFindProjectsCreatedAfter": 8, "tests\\unit\\chip\\infrastructure\\YiiProjectRepositoryTest::testFindProjectsWithStatus": 8, "tests\\unit\\chip\\infrastructure\\YiiProjectRepositoryTest::testDependencyInjection": 8}, "times": {"common\\chip\\alientech\\services\\AlientechProjectServiceTest::testProcessSuccessOperationDecode": 0.084, "common\\chip\\project\\services\\CreateProjectServiceTest::test__construct": 0.209, "common\\chip\\project\\services\\CreateProjectServiceTest::testRun": 0.019, "common\\chip\\project\\services\\CreateProjectServiceTest::testGetModelForm": 0.001, "ProjectCreationOrchestratorDatabaseTest::testCreateMinimalProject": 0, "ProjectCreationOrchestratorDatabaseTest::testCreateProjectWithOptions": 0, "ProjectCreationOrchestratorDatabaseTest::testCreateProjectWithFiles": 0, "tests\\integration\\AsyncEventDispatchTest::testAsyncEventDispatch": 0.389, "tests\\integration\\AsyncEventDispatchTest::testAsyncJobExecution": 0.368, "tests\\integration\\AsyncEventDispatchTest::testAsyncJobErrorHandling": 5.491, "tests\\integration\\RetryMechanismTest::testRetryOnError": 0.335, "tests\\integration\\RetryMechanismTest::testExponentialBackoff": 0.012, "tests\\integration\\Kess3DecodingTest::testCreateDecodeCommand": 0.133, "tests\\integration\\Kess3DecodingTest::testFileValidation": 0.026, "tests\\integration\\Kess3DecodingTest::testExecuteDecodeCommand": 0.145, "tests\\integration\\Kess3DecodingTest::testErrorHandling": 0.006, "tests\\integration\\Kess3DecodingTest::testHealthCheck": 0.017, "tests\\integration\\Kess3DecodingTest::testBatchExecution": 0.023, "tests\\unit\\kess3\\application\\CommandHandlerTest::testCreateStartDecodingCommand": 0.181, "tests\\unit\\kess3\\application\\CommandHandlerTest::testStartDecodingCommandValidation": 0.007, "tests\\unit\\kess3\\application\\CommandHandlerTest::testCommandSerialization": 0.007, "tests\\unit\\kess3\\application\\CommandHandlerTest::testStartDecodingHandlerSuccess": 0.098, "tests\\unit\\kess3\\application\\CommandHandlerTest::testStartDecodingHandlerApiError": 0.025, "tests\\unit\\kess3\\application\\CommandHandlerTest::testStartDecodingHandlerOperationExists": 0.024, "tests\\unit\\kess3\\application\\CommandHandlerTest::testCreateProcessDecodingResultCommand": 0.014, "tests\\unit\\kess3\\application\\CommandHandlerTest::testProcessDecodingResultHandlerSuccess": 0.007, "tests\\unit\\kess3\\application\\CommandHandlerTest::testProcessDecodingResultHandlerFailure": 0.007, "tests\\unit\\kess3\\application\\CommandHandlerTest::testProcessDecodingResultHandlerOperationNotFound": 0.007, "tests\\unit\\kess3\\application\\CommandHandlerTest::testCommandPriorityAndMetadata": 0.007, "tests\\unit\\kess3\\application\\CommandHandlerTest::testCommandRetryMechanism": 0.007, "tests\\unit\\kess3\\application\\CommandHandlerTest::testCommandTimeout": 0.007, "tests\\unit\\kess3\\application\\CommandHandlerTest::testCommandHandlerPerformance": 0.007, "tests\\unit\\kess3\\application\\CommandHandlerTest::testCommandImmutability": 0.007, "tests\\unit\\kess3\\application\\CommandHandlerTest::testCommandChaining": 0.007, "tests\\integration\\kess3\\CompleteDecodingFlowTest::testCompleteSuccessfulDecodingFlow": 0.23, "tests\\integration\\kess3\\CompleteDecodingFlowTest::testCompleteFailedDecodingFlow": 0.046, "tests\\integration\\kess3\\CompleteDecodingFlowTest::testOperationTimeoutFlow": 0.027, "tests\\integration\\kess3\\CompleteDecodingFlowTest::testConcurrentOperationsFlow": 0.028, "tests\\integration\\kess3\\CompleteDecodingFlowTest::testRecoveryAfterFailureFlow": 0.028, "tests\\integration\\kess3\\CompleteDecodingFlowTest::testFullCyclePerformance": 0.027, "tests\\integration\\kess3\\application\\Kess3DecodingFacadeTest::testStartDecoding": 0.028, "tests\\integration\\kess3\\application\\Kess3DecodingFacadeTest::testStartDecodingWithCallback": 0.028, "tests\\integration\\kess3\\application\\Kess3DecodingFacadeTest::testProcessDecodingResult": 0.029, "tests\\integration\\kess3\\application\\Kess3DecodingFacadeTest::testProcessDecodingError": 0.029, "tests\\integration\\kess3\\application\\Kess3DecodingFacadeTest::testGetProjectStatistics": 0.029, "tests\\integration\\kess3\\application\\Kess3DecodingFacadeTest::testGetProjectOperations": 0.029, "tests\\integration\\kess3\\application\\Kess3DecodingFacadeTest::testGetInProgressOperations": 0.029, "tests\\integration\\kess3\\application\\Kess3DecodingFacadeTest::testCancelExpiredOperations": 0.029, "tests\\integration\\kess3\\application\\Kess3DecodingFacadeTest::testGetSystemStatistics": 0.029, "tests\\integration\\kess3\\application\\Kess3DecodingFacadeTest::testHandleInvalidData": 0.045, "tests\\integration\\kess3\\application\\Kess3DecodingFacadeTest::testDuplicateDecodingRequest": 0.029, "tests\\integration\\kess3\\application\\Kess3DecodingFacadeTest::testFacadePerformance": 0.029, "tests\\integration\\kess3\\infrastructure\\DecodingConsoleControllerTest::testStartDecodingCommand": 0, "tests\\integration\\kess3\\infrastructure\\DecodingConsoleControllerTest::testStartDecodingWithCallbackCommand": 0, "tests\\integration\\kess3\\infrastructure\\DecodingConsoleControllerTest::testStatusCommand": 0, "tests\\integration\\kess3\\infrastructure\\DecodingConsoleControllerTest::testStatusCommandNotFound": 0, "tests\\integration\\kess3\\infrastructure\\DecodingConsoleControllerTest::testProjectStatsCommand": 0, "tests\\integration\\kess3\\infrastructure\\DecodingConsoleControllerTest::testCancelExpiredCommand": 0, "tests\\integration\\kess3\\infrastructure\\DecodingConsoleControllerTest::testListActiveCommand": 0, "tests\\integration\\kess3\\infrastructure\\DecodingConsoleControllerTest::testSystemStatsCommand": 0, "tests\\integration\\kess3\\infrastructure\\DecodingConsoleControllerTest::testCleanupCommand": 0, "tests\\integration\\kess3\\infrastructure\\DecodingConsoleControllerTest::testMonitorCommand": 0, "tests\\integration\\kess3\\infrastructure\\DecodingConsoleControllerTest::testRetryFailedCommand": 0, "tests\\integration\\kess3\\infrastructure\\DecodingConsoleControllerTest::testExportCommand": 0, "tests\\integration\\kess3\\infrastructure\\DecodingConsoleControllerTest::testCommandValidation": 0, "tests\\integration\\kess3\\infrastructure\\DecodingConsoleControllerTest::testInteractiveMode": 0, "tests\\integration\\kess3\\infrastructure\\DecodingConsoleControllerTest::testConsoleCommandsPerformance": 0, "tests\\integration\\kess3\\infrastructure\\DecodingConsoleControllerTest::testConsoleErrorHandling": 0, "tests\\integration\\kess3\\infrastructure\\DecodingConsoleControllerTest::testHelpCommand": 0, "tests\\integration\\kess3\\infrastructure\\DecodingEventHandlerTest::testHandleDecodingStartedEvent": 0, "tests\\integration\\kess3\\infrastructure\\DecodingEventHandlerTest::testHandleDecodingCompletedEvent": 0, "tests\\integration\\kess3\\infrastructure\\DecodingEventHandlerTest::testHandleDecodingFailedEvent": 0, "tests\\integration\\kess3\\infrastructure\\DecodingEventHandlerTest::testDownloadDecodedFiles": 0, "tests\\integration\\kess3\\infrastructure\\DecodingEventHandlerTest::testHandleUnknownEvent": 0, "tests\\integration\\kess3\\infrastructure\\DecodingEventHandlerTest::testHandleMultipleEvents": 0, "tests\\integration\\kess3\\infrastructure\\DecodingEventHandlerTest::testProjectNoteFormatting": 0, "tests\\integration\\kess3\\infrastructure\\DecodingEventHandlerTest::testEventBusIntegration": 0, "tests\\integration\\kess3\\infrastructure\\DecodingEventHandlerTest::testErrorHandling": 0, "tests\\integration\\kess3\\infrastructure\\DecodingEventHandlerTest::testEventHandlerPerformance": 0, "tests\\integration\\kess3\\infrastructure\\DecodingEventHandlerTest::testEventIdempotency": 0, "tests\\integration\\kess3\\infrastructure\\DecodingOperationRepositoryTest::testSaveOperation": 0, "tests\\integration\\kess3\\infrastructure\\DecodingOperationRepositoryTest::testFindById": 0, "tests\\integration\\kess3\\infrastructure\\DecodingOperationRepositoryTest::testFindByIdNotFound": 0, "tests\\integration\\kess3\\infrastructure\\DecodingOperationRepositoryTest::testFindByProjectId": 0, "tests\\integration\\kess3\\infrastructure\\DecodingOperationRepositoryTest::testFindByStatus": 0, "tests\\integration\\kess3\\infrastructure\\DecodingOperationRepositoryTest::testFindByExternalOperationId": 0, "tests\\integration\\kess3\\infrastructure\\DecodingOperationRepositoryTest::testFindExpiredOperations": 0, "tests\\integration\\kess3\\infrastructure\\DecodingOperationRepositoryTest::testDeleteOperation": 0, "tests\\integration\\kess3\\infrastructure\\DecodingOperationRepositoryTest::testUpdateOperation": 0, "tests\\integration\\kess3\\infrastructure\\DecodingOperationRepositoryTest::testGetOperationStatistics": 0, "tests\\integration\\kess3\\infrastructure\\DecodingOperationRepositoryTest::testPagination": 0, "tests\\integration\\kess3\\infrastructure\\DecodingOperationRepositoryTest::testSorting": 0, "tests\\integration\\kess3\\infrastructure\\DecodingOperationRepositoryTest::testFiltering": 0, "tests\\integration\\kess3\\infrastructure\\DecodingOperationRepositoryTest::testRepositoryPerformance": 0, "tests\\integration\\kess3\\interfaces\\DecodingControllerTest::testCallbackAction": 0.257, "tests\\integration\\kess3\\interfaces\\DecodingControllerTest::testCallbackActionWithError": 0.026, "tests\\integration\\kess3\\interfaces\\DecodingControllerTest::testStatusAction": 0.014, "tests\\integration\\kess3\\interfaces\\DecodingControllerTest::testStatusActionNotFound": 0.007, "tests\\integration\\kess3\\interfaces\\DecodingControllerTest::testProjectOperationsAction": 0.014, "tests\\integration\\kess3\\interfaces\\DecodingControllerTest::testCancelExpiredAction": 0.011, "tests\\integration\\kess3\\interfaces\\DecodingControllerTest::testStartDecodingAction": 0.007, "tests\\integration\\kess3\\interfaces\\DecodingControllerTest::testStartDecodingValidation": 0.007, "tests\\integration\\kess3\\interfaces\\DecodingControllerTest::testActiveOperationsAction": 0.013, "tests\\integration\\kess3\\interfaces\\DecodingControllerTest::testSystemStatisticsAction": 0.013, "tests\\integration\\kess3\\interfaces\\DecodingControllerTest::testExceptionHandling": 0.007, "tests\\integration\\kess3\\interfaces\\DecodingControllerTest::testCorsHeaders": 0.011, "tests\\integration\\kess3\\interfaces\\DecodingControllerTest::testControllerPerformance": 0.007, "tests\\integration\\kess3\\EventAndHandlerIntegrationTest::testProjectCreatedEventHandled": 0.026, "tests\\integration\\kess3\\EventAndHandlerIntegrationTest::testFileUploadedEventHandled": 0.013, "tests\\integration\\kess3\\EventAndHandlerIntegrationTest::testEncodingStartedEventHandled": 0.019, "tests\\integration\\kess3\\EventAndHandlerIntegrationTest::testEncodingCompletedEventHandled": 0.017, "tests\\integration\\kess3\\EventAndHandlerIntegrationTest::testDecodingStartedEventHandled": 0.017, "tests\\integration\\kess3\\EventAndHandlerIntegrationTest::testDecodingCompletedEventHandled": 0.016, "tests\\integration\\kess3\\FileDecodingTest::testFullDecodingProcess": 0.012, "tests\\integration\\kess3\\FileEncodingTest::testFullEncodingProcess": 0.012, "tests\\integration\\kess3\\NotificationEventHandlerIntegrationTest::testSystemErrorEventHandled": 0.014, "tests\\integration\\kess3\\NotificationEventHandlerIntegrationTest::testProjectStatusChangedEventHandled": 0.012, "tests\\integration\\kess3\\NotificationEventHandlerIntegrationTest::testNoteAddedEventHandled": 0.013, "tests\\integration\\kess3\\ProjectCreationTest::testProjectCreation": 0.012, "tests\\integration\\kess3\\IntegratedEncodingWithUploadTest::testCustomAdapterWithIntegratedUpload": 0.174, "tests\\integration\\kess3\\IntegratedEncodingWithUploadTest::testEncodingProcessFlow": 0.184, "tests\\integration\\kess3\\IntegratedEncodingWithUploadTest::testEncodingRequestDtoWithUploadOptions": 0.012, "tests\\integration\\kess3\\IntegratedEncodingWithUploadTest::testErrorHandlingInIntegratedProcess": 0, "tests\\integration\\kess3\\IntegratedEncodingWithUploadTest::testMultipleFilesEncodingSimulation": 0.011, "tests\\integration\\kess3\\IntegratedEncodingWithUploadTest::testAdapterFactoryWithDifferentServices": 0.014, "common\\chip\\externalIntegrations\\kess3\\Infrastructure\\Http\\HttpClientTest::testHttpClientCreation": 0.109, "common\\chip\\externalIntegrations\\kess3\\Infrastructure\\Http\\HttpClientTest::testHttpClientConfiguration": 0.008, "common\\chip\\externalIntegrations\\kess3\\Infrastructure\\Http\\HttpClientTest::testApiRequestCreation": 0.01, "common\\chip\\externalIntegrations\\kess3\\Infrastructure\\Http\\HttpClientTest::testApiRequestWithData": 0, "common\\chip\\externalIntegrations\\kess3\\Infrastructure\\Http\\HttpClientTest::testApiRequestWithFiles": 0, "common\\chip\\externalIntegrations\\kess3\\Infrastructure\\Http\\HttpClientTest::testAuthCredentials": 0.009, "common\\chip\\externalIntegrations\\kess3\\Infrastructure\\Http\\HttpClientTest::testAuthCredentialsInvalid": 0, "tests\\integration\\kess3\\CompleteEncodingFlowTest::testCompleteEncodingFlowWithCustomAdapter": 0.176, "tests\\integration\\kess3\\CompleteEncodingFlowTest::testEncodingFacadeIntegration": 0.021, "tests\\integration\\kess3\\CompleteEncodingFlowTest::testFactoryCreatesAllSupportedAdapters": 0.008, "tests\\integration\\kess3\\CompleteEncodingFlowTest::testEncodingWithDifferentAdapters": 0.007, "tests\\unit\\chip\\domain\\ValueObjectsTest::testProjectId": 0.009, "tests\\unit\\chip\\domain\\ValueObjectsTest::testProjectStatus": 0.041, "tests\\unit\\chip\\domain\\ValueObjectsTest::testProjectStatusTransitions": 0, "tests\\unit\\chip\\domain\\ValueObjectsTest::testMoney": 0.013, "tests\\unit\\chip\\domain\\ValueObjectsTest::testCurrency": 0, "tests\\unit\\chip\\domain\\ValueObjectsTest::testFileType": 0.004, "tests\\unit\\chip\\domain\\ValueObjectsTest::testFileStatus": 0.004, "tests\\unit\\chip\\domain\\ValueObjectsTest::testFileName": 0.004, "tests\\unit\\chip\\domain\\ValueObjectsTest::testBrand": 0.004, "tests\\unit\\chip\\domain\\ValueObjectsTest::testImmutability": 0.009, "tests\\unit\\chip\\domain\\ValueObjectsTest::testIdUniqueness": 0.009}}