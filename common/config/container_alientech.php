<?php

use common\chip\alientech\services\AlientechService;
use common\chip\event\EventDispatcher;
use common\chip\externalIntegrations\alientech\Application\Decoding\EventHandler\DecodingEventHandler;
use common\chip\externalIntegrations\alientech\Application\Decoding\EventHandler\EncodingEventHandler;
use common\chip\externalIntegrations\alientech\Application\Decoding\Handler\ProcessDecodingResultHandler;
use common\chip\externalIntegrations\alientech\Application\Decoding\Handler\StartDecodingHandler;
use common\chip\externalIntegrations\alientech\Application\Encoding\Handler\StartEncodingHandler;
use common\chip\externalIntegrations\alientech\Domain\Decoding\Repository\DecodingRepositoryInterface;
use common\chip\externalIntegrations\alientech\Domain\Decoding\Service\DecodingDomainService;
use common\chip\externalIntegrations\alientech\Domain\Encoding\Repository\EncodingRepositoryInterface;
use common\chip\externalIntegrations\alientech\Domain\Encoding\Service\EncodingDomainService;
use common\chip\externalIntegrations\alientech\Infrastructure\Configuration\ConfigProviderInterface;
use common\chip\externalIntegrations\alientech\Infrastructure\ExternalService\AlientechApiClient;
use common\chip\externalIntegrations\alientech\Infrastructure\ExternalService\AlientechApiClientInterface;
use common\chip\externalIntegrations\alientech\Infrastructure\Facade\DecodingFacade;
use common\chip\externalIntegrations\alientech\Infrastructure\Facade\EncodingFacade;
use common\chip\externalIntegrations\alientech\Infrastructure\Http\AuthProvider;
use common\chip\externalIntegrations\alientech\Infrastructure\Http\AuthProviderInterface;
use common\chip\externalIntegrations\alientech\Infrastructure\Http\HttpClientFactory;
use common\chip\externalIntegrations\alientech\Infrastructure\Http\HttpClientInterface;
use common\chip\externalIntegrations\alientech\Infrastructure\Repository\DecodingRepository;
use common\chip\externalIntegrations\alientech\Infrastructure\Repository\EncodingRepository;
use common\chip\externalIntegrations\alientech\Infrastructure\SlotManagement\SlotManagerInterface;
use common\chip\externalIntegrations\alientech\Infrastructure\Testing\MockAlientechApiClient;

return [
    // ========================================
    // CORE INTERFACES
    // ========================================

    DecodingRepositoryInterface::class => DecodingRepository::class,
    EncodingRepositoryInterface::class => EncodingRepository::class,
    AlientechApiClientInterface::class => AlientechApiClient::class,

    // ========================================
    // DOMAIN SERVICES
    // ========================================

    DecodingDomainService::class => function ($container) {
        return new DecodingDomainService(
            $container->get(DecodingRepositoryInterface::class)
        );
    },

    EncodingDomainService::class => function ($container) {
        return new EncodingDomainService(
            $container->get(EncodingRepositoryInterface::class)
        );
    },

    // ========================================
    // INFRASTRUCTURE REPOSITORIES
    // ========================================

    DecodingRepository::class => function () {
        return new DecodingRepository();
    },

    EncodingRepository::class => function () {
        return new EncodingRepository();
    },

    // ========================================
    // INFRASTRUCTURE EXTERNAL SERVICES
    // ========================================

    AlientechApiClient::class => function ($container) {
        return new AlientechApiClient(
            $container->get(HttpClientInterface::class),
            $container->get(ConfigProviderInterface::class),
            $container->get(SlotManagerInterface::class)
        );
    },

    // ========================================
    // HTTP INFRASTRUCTURE
    // ========================================

    HttpClientFactory::class => function ($container) {
        return new HttpClientFactory(
            \Yii::$app->db,
            \Yii::$app->cache
        );
    },

    HttpClientInterface::class => function ($container) {
        $factory = $container->get(HttpClientFactory::class);
        $config = $container->get(ConfigProviderInterface::class);
        return $factory->createHttpClient($config);
    },

    AuthProviderInterface::class => function ($container) {
        return new AuthProvider(
            \Yii::$app->db,
            \Yii::$app->cache
        );
    },

    ConfigProviderInterface::class => function () {
        return new class implements ConfigProviderInterface {
            public function getCustomerCode(): string { return 'test_customer'; }
            public function getCallbackUrl(string $operationType): string { return 'http://localhost/callback'; }
            public function getApiEndpoint(string $endpoint): string { return "http://localhost/api/{$endpoint}"; }
            public function getApiBaseUrl(): string { return 'http://localhost/api'; }
            public function getAuthCredentials(): \common\chip\externalIntegrations\alientech\Infrastructure\Dto\AuthCredentials {
                return new \common\chip\externalIntegrations\alientech\Infrastructure\Dto\AuthCredentials('test', 'test');
            }
            public function getTimeout(string $operationType): int { return 30; }
            public function getTimeouts(): array { return ['default' => 30, 'upload' => 60]; }
            public function getRetrySettings(): array { return ['max_attempts' => 3, 'delay' => 1, 'backoff_multiplier' => 2.0]; }
        };
    },

    SlotManagerInterface::class => function () {
        return new class implements SlotManagerInterface {
            public function hasAvailableSlots(): bool { return true; }
            public function getSlots(): array { return []; }
            public function closeSlot(string $slotGuid): bool { return true; }
            public function closeAllSlots(): array { return ['closed' => 0, 'failed' => 0]; }
            public function reopenSlot(string $slotGuid): bool { return true; }
            public function getMaxSlots(): int { return 10; }
            public function getOpenSlotsCount(): int { return 0; }
            public function clearCache(): void {}
        };
    },

    // ========================================
    // APPLICATION HANDLERS
    // ========================================

    StartDecodingHandler::class => function ($container) {
        return new StartDecodingHandler(
            $container->get(DecodingDomainService::class),
            $container->get(DecodingRepositoryInterface::class),
            $container->get(AlientechApiClientInterface::class),
            $container->get(EventDispatcher::class)
        );
    },

    StartEncodingHandler::class => function ($container) {
        return new StartEncodingHandler(
            $container->get(EncodingDomainService::class),
            $container->get(EncodingRepositoryInterface::class),
            $container->get(AlientechApiClientInterface::class),
            $container->get(EventDispatcher::class)
        );
    },

    DecodingEventHandler::class => function ($container) {
        return new DecodingEventHandler(
            $container->get(AlientechApiClientInterface::class),
            $container->get(EventDispatcher::class)
        );
    },

    EncodingEventHandler::class => function ($container) {
        return new EncodingEventHandler(
            $container->get(AlientechApiClientInterface::class),
            $container->get(EventDispatcher::class)
        );
    },

    // ========================================
    // LEGACY SERVICES (используем существующие)
    // ========================================

    AlientechService::class => function () {
        return new AlientechService();
    },

    EventDispatcher::class => function () {
        return Yii::$container->get(EventDispatcher::class);
    },

    // ========================================
    // FACADES (для удобства использования)
    // ========================================

    DecodingFacade::class => function ($container) {
        return new DecodingFacade(
            $container->get(StartDecodingHandler::class),
            $container->get(ProcessDecodingResultHandler::class)
        );
    },

    EncodingFacade::class => function ($container) {
        return new EncodingFacade(
            $container->get(StartEncodingHandler::class)
        );
    },

    'alientech.decoding.facade' => function ($container) {
        return $container->get(DecodingFacade::class);
    },

    'alientech.encoding.facade' => function ($container) {
        return $container->get(EncodingFacade::class);
    },

    // ========================================
    // TESTING DEPENDENCIES
    // ========================================

    'alientech.test.decoding_repository' => function () {
        return new DecodingRepository();
    },

    'alientech.test.encoding_repository' => function () {
        return new EncodingRepository();
    },

    MockAlientechApiClient::class => function () {
        return new MockAlientechApiClient();
    },

    'alientech.test.mock_api_client' => function ($container) {
        return $container->get(MockAlientechApiClient::class);
    },
];
