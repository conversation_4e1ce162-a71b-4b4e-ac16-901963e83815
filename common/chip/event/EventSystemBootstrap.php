<?php

namespace common\chip\event;

use common\chip\event\routing\resolver\DefaultChannelSelector;
use common\chip\externalIntegrations\kess3\Infrastructure\EventHandler\DecodingEventHandler;
use common\chip\notification\services\NotificationMonitoringService;
use common\chip\notification\channel\UiChannel;
use common\chip\notification\channel\TelegramChannel;
use common\repositories\UserRepository;
use common\services\TelegramApiService;
use Yii;

/**
 * Инициализатор событийной системы
 */
class EventSystemBootstrap
{
    /**
     * @var EventDispatcher|null Экземпляр диспетчера событий
     */
    private static ?EventDispatcher $dispatcher = null;

    /**
     * @var array Зарегистрированные обработчики
     */
    private static array $handlers = [];

    /**
     * Инициализирует событийную систему и возвращает диспетчер событий
     *
     * @return EventDispatcher
     */
    public static function getDispatcher(): EventDispatcher
    {
        if (self::$dispatcher === null) {
            self::$dispatcher = new EventDispatcher();

            // Регистрируем обработчики
            foreach (self::$handlers as $handler) {
                self::$dispatcher->registerHandler($handler);
            }
        }

        return self::$dispatcher;
    }

    /**
     * Устанавливает диспетчер событий
     *
     * @param EventDispatcher $dispatcher Диспетчер событий
     * @return void
     */
    public static function setDispatcher(EventDispatcher $dispatcher): void
    {
        self::$dispatcher = $dispatcher;

        // Регистрируем обработчики в новом диспетчере
        foreach (self::$handlers as $handler) {
            self::$dispatcher->registerHandler($handler);
        }
    }

    /**
     * Регистрирует обработчик событий
     *
     * @param EventHandler $handler Обработчик для регистрации
     * @return void
     */
    public static function registerHandler(EventHandler $handler): void
    {
        self::$handlers[] = $handler;

        // Если диспетчер уже инициализирован, зарегистрируем обработчик напрямую
        if (self::$dispatcher !== null) {
            self::$dispatcher->registerHandler($handler);
        }
    }

    /**
     * Настраивает компоненты событийной системы для контейнера Yii
     * после его инициализации
     *
     * @param \yii\di\Container $container Контейнер Yii
     * @return void
     */
    public static function configureDI(\yii\di\Container $container): void
    {
        // Регистрируем диспетчер как синглтон
        $container->setSingleton(EventDispatcher::class, function () {
            return self::getDispatcher();
        });

        // Регистрируем репозиторий событий
        $container->setSingleton(\common\chip\event\repositories\EventRepository::class);

        // Регистрируем фабрику событий проекта
        $container->set(\common\chip\event\project\ProjectEventFactory::class);
        $container->set(\common\chip\event\project\FileEventFactory::class);

        // Регистрируем компоненты системы уведомлений
        self::configureNotificationComponents($container);
    }

    /**
     * Настраивает компоненты системы уведомлений
     *
     * @param \yii\di\Container $container Контейнер Yii
     * @return void
     */
    private static function configureNotificationComponents(\yii\di\Container $container): void
    {
        // Регистрируем NotificationMonitoringService как синглтон
        $container->setSingleton(NotificationMonitoringService::class, function () {
            return new NotificationMonitoringService();
        });

        // Регистрируем TelegramApiService как синглтон
        $container->setSingleton(TelegramApiService::class, function () {
            return new TelegramApiService();
        });

        // Регистрируем DefaultChannelSelector как синглтон с автоматической регистрацией каналов
        $container->setSingleton(DefaultChannelSelector::class, function () use ($container) {
            // Загружаем конфигурацию каналов
            $channelConfig = self::getChannelConfig();

            // Создаем селектор каналов
            $channelSelector = new DefaultChannelSelector($channelConfig);

            // Регистрируем все каналы по умолчанию
            self::registerDefaultChannels($channelSelector, $container);

            return $channelSelector;
        });
    }

    /**
     * Регистрирует все каналы по умолчанию
     *
     * @param DefaultChannelSelector $channelSelector Селектор каналов
     * @param \yii\di\Container $container Контейнер зависимостей
     * @return void
     */
    private static function registerDefaultChannels(DefaultChannelSelector $channelSelector, \yii\di\Container $container): void
    {
        try {
            $monitoringService = $container->get(NotificationMonitoringService::class);
            $telegramApiService = $container->get(TelegramApiService::class);

            // Регистрируем UI канал (всегда доступен)
            $uiChannel = new UiChannel(
                $monitoringService,
                self::getChannelConfigFromDb('ui_popup')
            );
            $channelSelector->registerChannel($uiChannel);

            // Регистрируем Telegram канал
            $telegramChannel = new TelegramChannel(
                $monitoringService,
                $telegramApiService,
                self::getChannelConfigFromDb('telegram')
            );
            $channelSelector->registerChannel($telegramChannel);

            Yii::info([
                'message' => 'Каналы уведомлений зарегистрированы',
                'registeredChannels' => array_keys($channelSelector->getAllChannels())
            ], 'notification');

        } catch (\Exception $e) {
            Yii::error([
                'message' => 'Ошибка регистрации каналов уведомлений',
                'error' => $e->getMessage()
            ], 'notification');
        }
    }

    /**
     * Получает конфигурацию каналов
     *
     * @return array
     */
    private static function getChannelConfig(): array
    {
        // Конфигурация каналов по умолчанию
        return [
            'ProjectCreatedEvent' => ['ui_popup', 'telegram'],
            'FileUploadedEvent' => ['ui_popup', 'telegram'],
            'ProjectNoteAddedEvent' => ['ui_popup', 'telegram'],
            'NoteAddedEvent' => ['ui_popup', 'telegram'],
            'ProjectStatusChangedEvent' => ['ui_popup', 'telegram'],
            'SystemErrorEvent' => ['ui_popup', 'telegram'],
            'FileProcessedEvent' => ['ui_popup', 'telegram'],
            'OptionsRequestEvent' => ['ui_popup', 'telegram'],
            'default' => ['ui_popup'],
            'role_restrictions' => [
                // Пока нет ограничений по ролям для UI и Telegram каналов
            ]
        ];
    }

    /**
     * Получает конфигурацию канала из БД
     *
     * @param string $channelCode Код канала
     * @return array
     */
    private static function getChannelConfigFromDb(string $channelCode): array
    {
        try {
            $channel = \common\models\notification\NotificationChannel::findOne(['code' => $channelCode, 'is_active' => true]);
            if ($channel && !empty($channel->config)) {
                return json_decode($channel->config, true) ?: [];
            }
        } catch (\Exception $e) {
            Yii::warning("Ошибка получения конфигурации канала {$channelCode}: " . $e->getMessage(), 'notification');
        }

        return [];
    }

    /**
     * Получает UserRepository
     *
     * @return UserRepository
     */
    private static function getUserRepository(): UserRepository
    {
        try {
            return Yii::$container->get(UserRepository::class);
        } catch (\Exception $e) {
            // Fallback: создаем новый экземпляр
            return new UserRepository();
        }
    }


}
