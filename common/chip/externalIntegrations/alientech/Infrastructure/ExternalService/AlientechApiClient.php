<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Infrastructure\ExternalService;

use common\chip\externalIntegrations\alientech\Application\Decoding\Handler\AlientechApiClientInterface;
use common\chip\externalIntegrations\alientech\Infrastructure\Configuration\ConfigProviderInterface;
use common\chip\externalIntegrations\alientech\Infrastructure\Dto\ApiResponse;
use common\chip\externalIntegrations\alientech\Infrastructure\Http\HttpClientInterface;
use common\chip\externalIntegrations\alientech\Infrastructure\SlotManagement\SlotManagerInterface;
use common\chip\externalIntegrations\alientech\Infrastructure\Dto\ApiRequest;
use common\chip\externalIntegrations\alientech\Domain\Exception\ApiException;
use Yii;

/**
 * Клиент для работы с Alientech API
 */
final class AlientechApiClient implements AlientechApiClientInterface
{
    public function __construct(
        private readonly HttpClientInterface $httpClient,
        private readonly ConfigProviderInterface $configProvider,
        private readonly SlotManagerInterface $slotManager
    ) {
    }

    public function startDecoding(string $filePath, string $callbackUrl, array $userInfo): array
    {
        try {
            // Проверяем доступность слотов
            if (!$this->slotManager->hasAvailableSlots()) {
                throw new ApiException('No available slots for decoding operation');
            }

            // Подготавливаем данные для запроса
            $requestData = [
                'customerCode' => $this->configProvider->getCustomerCode(),
                'callbackUrl' => $callbackUrl,
                'userInfo' => json_encode($userInfo),
            ];

            // Создаем запрос для загрузки файла и запуска декодирования
            $request = new ApiRequest(
                method: 'POST',
                url: $this->configProvider->getApiEndpoint('decode'),
                data: $requestData,
                files: ['file' => $filePath],
                timeout: $this->configProvider->getTimeout('upload')
            );
            if (YII_DEBUG) {
                $dataString = file_get_contents(Yii::getAlias('@storage') . '/payload/alientechOperation5Started.json');
                $data = json_decode($dataString, true);

                $response = new ApiResponse(200, [], $data, $dataString);//$this->processResponse($response);
            } else {
                $response = $this->httpClient->sendRequest($request);
            }

            if (!$response->isSuccessful()) {
                throw ApiException::fromApiResponse($response->getDataAsArray(), $response->getStatusCode());
            }

            return $response->getDataAsArray();
        } catch (\Exception $e) {
            if ($e instanceof ApiException) {
                throw $e;
            }
            throw new ApiException("Failed to start decoding: {$e->getMessage()}", 0, $e);
        }
    }

    public function startEncoding(array $filePaths, string $callbackUrl, array $userInfo): array
    {
        try {
            // Проверяем доступность слотов
            if (!$this->slotManager->hasAvailableSlots()) {
                throw new ApiException('No available slots for encoding operation');
            }

            // Подготавливаем данные для запроса
            $requestData = [
                'customerCode' => $this->configProvider->getCustomerCode(),
                'callbackUrl' => $callbackUrl,
                'userInfo' => json_encode($userInfo),
            ];

            // Создаем запрос для загрузки файлов и запуска кодирования
            $request = new ApiRequest(
                method: 'POST',
                url: $this->configProvider->getApiEndpoint('encode'),
                data: $requestData,
                timeout: $this->configProvider->getTimeout('upload')
            );

            // Добавляем файлы к запросу
            foreach ($filePaths as $index => $filePath) {
                $request->addFile("file_{$index}", $filePath);
            }

            $response = $this->httpClient->sendRequest($request);

            if (!$response->isSuccessful()) {
                throw ApiException::fromApiResponse($response->getDataAsArray(), $response->getStatusCode());
            }

            return $response->getDataAsArray();
        } catch (\Exception $e) {
            if ($e instanceof ApiException) {
                throw $e;
            }
            throw new ApiException("Failed to start encoding: {$e->getMessage()}", 0, $e);
        }
    }

    public function getOperationStatus(string $externalOperationId): array
    {
        try {
            $request = new ApiRequest(
                method: 'GET',
                url: $this->configProvider->getApiEndpoint('status') . '/' . $externalOperationId,
                timeout: $this->configProvider->getTimeout('default')
            );

            $response = $this->httpClient->sendRequest($request);

            if (!$response->isSuccessful()) {
                throw ApiException::fromApiResponse($response->getDataAsArray(), $response->getStatusCode());
            }

            return $response->getDataAsArray();
        } catch (\Exception $e) {
            if ($e instanceof ApiException) {
                throw $e;
            }
            throw new ApiException("Failed to get operation status: {$e->getMessage()}", 0, $e);
        }
    }

    public function cancelOperation(string $externalOperationId): bool
    {
        try {
            $request = new ApiRequest(
                method: 'DELETE',
                url: $this->configProvider->getApiEndpoint('cancel') . '/' . $externalOperationId,
                timeout: $this->configProvider->getTimeout('default')
            );

            $response = $this->httpClient->sendRequest($request);

            return $response->isSuccessful();
        } catch (\Exception $e) {
            return false;
        }
    }
}
