<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Infrastructure\Testing;

use common\chip\externalIntegrations\alientech\Infrastructure\ExternalService\AlientechApiClientInterface;

/**
 * Мок API клиента для тестирования
 */
final class MockAlientechApiClient implements AlientechApiClientInterface
{
    public function startDecoding(string $filePath, string $callbackUrl, array $userInfo): array
    {
        return [
            'guid' => 'test_guid_' . uniqid(),
            'slotGUID' => 'test_slot_' . uniqid(),
        ];
    }

    public function startEncoding(array $filePaths, string $callbackUrl, array $userInfo): array
    {
        return [
            'guid' => 'test_guid_' . uniqid(),
            'slotGUID' => 'test_slot_' . uniqid(),
        ];
    }

    public function getOperationStatus(string $externalOperationId): array
    {
        return [
            'status' => 'in_progress',
            'isCompleted' => false,
        ];
    }

    public function cancelOperation(string $externalOperationId): bool
    {
        return true;
    }
}
