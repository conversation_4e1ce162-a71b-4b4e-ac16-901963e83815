<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Infrastructure\SlotManagement;

use common\chip\externalIntegrations\kess3\Infrastructure\Http\HttpClientInterface;
use common\chip\externalIntegrations\kess3\Infrastructure\Dto\ApiRequest;
use common\chip\externalIntegrations\kess3\Infrastructure\Dto\SlotInfo;
use common\chip\externalIntegrations\kess3\Domain\Exception\ApiException;
use common\chip\externalIntegrations\kess3\Domain\Exception\SlotLimitException;
use Psr\Log\LoggerInterface;
use yii\caching\CacheInterface;

/**
 * Менеджер файловых слотов для Alientech API
 */
final class SlotManager implements SlotManagerInterface
{
    private const MAXIMUM_KESS_FILE_SLOTS = 3;
    private const CACHE_KEY_SLOTS = 'kess3_alientech_slots';
    
    private ?array $cachedSlots = null;
    private ?\DateTimeInterface $lastCacheTime = null;
    
    public function __construct(
        private readonly HttpClientInterface $httpClient,
        private readonly CacheInterface $cache,
        private readonly LoggerInterface $logger,
        private readonly int $cacheDuration = 60,
        private readonly int $maxSlots = self::MAXIMUM_KESS_FILE_SLOTS
    ) {}
    
    public function hasAvailableSlots(): bool
    {
        $this->logger->info('SlotManager: Checking available slots');
        if (YII_DEBUG) {
            return true;
        }
        try {
            $slots = $this->getSlots();
            $openSlots = $this->countOpenSlots($slots);
            
            $hasAvailable = $openSlots < $this->maxSlots;
            
            $this->logger->info('SlotManager: Slot check result', [
                'totalSlots' => count($slots),
                'openSlots' => $openSlots,
                'maxSlots' => $this->maxSlots,
                'hasAvailable' => $hasAvailable
            ]);
            
            return $hasAvailable;
            
        } catch (\Throwable $e) {
            $this->logger->error('SlotManager: Error checking slots', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw new ApiException('Failed to check available slots: ' . $e->getMessage(), 0, $e);
        }
    }
    
    public function getSlots(): array
    {
        $this->logger->info('SlotManager: Getting slots');
        
        // Проверяем кеш
        if ($this->isCacheValid()) {
            $this->logger->info('SlotManager: Using cached slots');
            return $this->cachedSlots;
        }
        
        try {
            // Запрашиваем слоты с API
            $request = new ApiRequest('GET', '/api/kess3/file-slots');
            $response = $this->httpClient->sendRequest($request);
            
            if (!$response->isSuccessful()) {
                throw new ApiException(
                    'Failed to get slots: HTTP ' . $response->getStatusCode(),
                    $response->getStatusCode()
                );
            }
            
            $data = $response->getData();
            
            if (!is_array($data)) {
                $this->logger->warning('SlotManager: Invalid slots response format', [
                    'data' => $data
                ]);
                $data = [];
            }
            
            // Преобразуем в SlotInfo объекты
            $slots = [];
            foreach ($data as $slotData) {
                if (is_object($slotData) || is_array($slotData)) {
                    $slotArray = is_object($slotData) ? (array) $slotData : $slotData;
                    $slots[] = SlotInfo::fromApiData($slotArray);
                }
            }
            
            // Кешируем результат
            $this->cacheSlots($slots);
            
            $this->logger->info('SlotManager: Slots retrieved', [
                'count' => count($slots)
            ]);
            
            return $slots;
            
        } catch (ApiException $e) {
            throw $e;
        } catch (\Throwable $e) {
            $this->logger->error('SlotManager: Error getting slots', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw new ApiException('Failed to get slots: ' . $e->getMessage(), 0, $e);
        }
    }
    
    public function closeSlot(string $slotGuid): bool
    {
        $this->logger->info('SlotManager: Closing slot', ['slotGuid' => $slotGuid]);
        
        if (empty($slotGuid)) {
            throw new \InvalidArgumentException('Slot GUID cannot be empty');
        }
        
        try {
            $url = '/api/kess3/file-slots/' . $slotGuid . '/close';
            $request = new ApiRequest('POST', $url);
            $request->addHeader('Content-Length', '0');
            
            $response = $this->httpClient->sendRequest($request);
            
            if (!$response->isSuccessful()) {
                $this->logger->error('SlotManager: Failed to close slot', [
                    'slotGuid' => $slotGuid,
                    'status' => $response->getStatusCode(),
                    'response' => $response->getRawContent()
                ]);
                
                throw new ApiException(
                    'Failed to close slot: HTTP ' . $response->getStatusCode(),
                    $response->getStatusCode()
                );
            }
            
            // Очищаем кеш после изменения
            $this->clearCache();
            
            $this->logger->info('SlotManager: Slot closed successfully', ['slotGuid' => $slotGuid]);
            
            return true;
            
        } catch (ApiException $e) {
            throw $e;
        } catch (\Throwable $e) {
            $this->logger->error('SlotManager: Error closing slot', [
                'slotGuid' => $slotGuid,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw new ApiException('Failed to close slot: ' . $e->getMessage(), 0, $e);
        }
    }
    
    public function closeAllSlots(): array
    {
        $this->logger->info('SlotManager: Closing all slots');
        
        try {
            $slots = $this->getSlots();
            $results = ['closed' => 0, 'failed' => 0, 'details' => []];
            
            foreach ($slots as $slot) {
                if ($slot->isOpen()) {
                    try {
                        $this->logger->info('SlotManager: Closing slot', [
                            'slotGuid' => $slot->getGuid(),
                            'ageInMinutes' => $slot->getAgeInMinutes()
                        ]);
                        
                        $success = $this->closeSlot($slot->getGuid());
                        
                        if ($success) {
                            $results['closed']++;
                            $results['details'][] = [
                                'guid' => $slot->getGuid(),
                                'status' => 'closed',
                                'ageInMinutes' => $slot->getAgeInMinutes()
                            ];
                        } else {
                            $results['failed']++;
                            $results['details'][] = [
                                'guid' => $slot->getGuid(),
                                'status' => 'failed',
                                'error' => 'Unknown error'
                            ];
                        }
                        
                    } catch (\Throwable $e) {
                        $results['failed']++;
                        $results['details'][] = [
                            'guid' => $slot->getGuid(),
                            'status' => 'failed',
                            'error' => $e->getMessage()
                        ];
                        
                        $this->logger->error('SlotManager: Failed to close individual slot', [
                            'slotGuid' => $slot->getGuid(),
                            'error' => $e->getMessage()
                        ]);
                    }
                }
            }
            
            $this->logger->info('SlotManager: Close all slots completed', $results);
            
            return $results;
            
        } catch (\Throwable $e) {
            $this->logger->error('SlotManager: Error closing all slots', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw new ApiException('Failed to close all slots: ' . $e->getMessage(), 0, $e);
        }
    }
    
    public function reopenSlot(string $slotGuid): bool
    {
        $this->logger->info('SlotManager: Reopening slot', ['slotGuid' => $slotGuid]);
        
        if (empty($slotGuid)) {
            throw new \InvalidArgumentException('Slot GUID cannot be empty');
        }
        
        try {
            // Проверяем, есть ли доступные слоты
            if (!$this->hasAvailableSlots()) {
                $this->logger->info('SlotManager: No available slots, closing all first');
                $this->closeAllSlots();
            }
            
            $url = '/api/kess3/file-slots/' . $slotGuid . '/reopen';
            $request = new ApiRequest('POST', $url);
            $request->addHeader('Content-Length', '0');
            
            $response = $this->httpClient->sendRequest($request);
            
            if (!$response->isSuccessful()) {
                $this->logger->error('SlotManager: Failed to reopen slot', [
                    'slotGuid' => $slotGuid,
                    'status' => $response->getStatusCode(),
                    'response' => $response->getRawContent()
                ]);
                
                throw new ApiException(
                    'Failed to reopen slot: HTTP ' . $response->getStatusCode(),
                    $response->getStatusCode()
                );
            }
            
            // Очищаем кеш после изменения
            $this->clearCache();
            
            $this->logger->info('SlotManager: Slot reopened successfully', ['slotGuid' => $slotGuid]);
            
            return true;
            
        } catch (ApiException $e) {
            throw $e;
        } catch (\Throwable $e) {
            $this->logger->error('SlotManager: Error reopening slot', [
                'slotGuid' => $slotGuid,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw new ApiException('Failed to reopen slot: ' . $e->getMessage(), 0, $e);
        }
    }
    
    public function getMaxSlots(): int
    {
        return $this->maxSlots;
    }
    
    public function getOpenSlotsCount(): int
    {
        $slots = $this->getSlots();
        return $this->countOpenSlots($slots);
    }
    
    public function clearCache(): void
    {
        $this->logger->info('SlotManager: Clearing cache');
        
        $this->cachedSlots = null;
        $this->lastCacheTime = null;
        $this->cache->delete(self::CACHE_KEY_SLOTS);
    }
    
    /**
     * Подсчитывает количество открытых слотов
     */
    private function countOpenSlots(array $slots): int
    {
        $openSlots = 0;
        
        foreach ($slots as $slot) {
            if ($slot instanceof SlotInfo && $slot->isOpen()) {
                $openSlots++;
            }
        }
        
        return $openSlots;
    }
    
    /**
     * Проверяет валидность кеша
     */
    private function isCacheValid(): bool
    {
        if ($this->cachedSlots === null || $this->lastCacheTime === null) {
            return false;
        }
        
        $now = new \DateTimeImmutable();
        $diff = $now->getTimestamp() - $this->lastCacheTime->getTimestamp();
        
        return $diff < $this->cacheDuration;
    }
    
    /**
     * Кеширует слоты
     */
    private function cacheSlots(array $slots): void
    {
        $this->cachedSlots = $slots;
        $this->lastCacheTime = new \DateTimeImmutable();
        
        // Сериализуем для кеша
        $cacheData = [
            'slots' => array_map(fn(SlotInfo $slot) => $slot->toArray(), $slots),
            'timestamp' => $this->lastCacheTime->getTimestamp()
        ];
        
        $this->cache->set(self::CACHE_KEY_SLOTS, $cacheData, $this->cacheDuration);
    }
}
