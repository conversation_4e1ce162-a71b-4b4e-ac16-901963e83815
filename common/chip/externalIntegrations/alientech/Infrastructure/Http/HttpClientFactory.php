<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Infrastructure\Http;

use common\chip\externalIntegrations\alientech\Infrastructure\Configuration\ConfigProviderInterface;
use yii\db\Connection;
use yii\caching\CacheInterface;

/**
 * Фабрика для создания HTTP компонентов
 */
final readonly class HttpClientFactory
{
    public function __construct(
        private Connection $db,
        private CacheInterface $cache
    ) {}

    /**
     * Создает настроенный HTTP клиент с аутентификацией
     */
    public function createHttpClient(ConfigProviderInterface $config): HttpClientInterface
    {
        // Создаем провайдер аутентификации
        $authProvider = $this->createAuthProvider($config);

        // Создаем HTTP клиент
        $httpClient = new HttpClient();
        $httpClient->setBaseUrl($config->getApiBaseUrl());
        $httpClient->setDefaultTimeout($config->getTimeout('default'));
        $httpClient->setAuthProvider($authProvider);

        return $httpClient;
    }

    /**
     * Создает провайдер аутентификации
     */
    public function createAuthProvider(ConfigProviderInterface $config): AuthProviderInterface
    {
        return new AuthProvider(
            db: $this->db,
            cache: $this->cache,
            authUrl: $config->getApiEndpoint('auth'),
            tokenCacheDuration: 3600,
            refreshThreshold: 300
        );
    }

    /**
     * Создает простой HTTP клиент без аутентификации (для публичных API)
     */
    public function createSimpleHttpClient(string $baseUrl, int $timeout = 30): HttpClientInterface
    {
        $httpClient = new HttpClient();
        $httpClient->setBaseUrl($baseUrl);
        $httpClient->setDefaultTimeout($timeout);

        return $httpClient;
    }
}
