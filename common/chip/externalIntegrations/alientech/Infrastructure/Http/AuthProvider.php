<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Infrastructure\Http;

use common\chip\externalIntegrations\alientech\Infrastructure\Dto\AuthCredentials;
use common\chip\externalIntegrations\alientech\Domain\Exception\AuthenticationException;
use yii\db\Connection;
use yii\caching\CacheInterface;

/**
 * Провайдер аутентификации для Alientech API
 */
final class AuthProvider implements AuthProviderInterface
{
    private const TOKEN_CACHE_KEY = 'alientech_auth_token';
    private const TOKEN_EXPIRY_CACHE_KEY = 'alientech_token_expiry';
    
    private ?string $accessToken = null;
    private ?\DateTimeInterface $tokenExpiry = null;
    
    public function __construct(
        private readonly Connection $db,
        private readonly CacheInterface $cache,
        private readonly string $authUrl = '/api/access-tokens/request',
        private readonly int $tokenCacheDuration = 3600,
        private readonly int $refreshThreshold = 300
    ) {
        $this->loadTokenFromCache();
    }
    
    public function getToken(): string
    {
        if ($this->isValid()) {
            return $this->accessToken;
        }
        
        if (!$this->refreshToken()) {
            throw AuthenticationException::tokenExpired();
        }
        
        return $this->accessToken;
    }
    
    public function refreshToken(): bool
    {
        try {
            // Очищаем текущий токен
            $this->clearToken();
            
            // Получаем учетные данные из БД
            $credentials = $this->getCredentialsFromDatabase();
            
            // Выполняем аутентификацию
            return $this->authenticate($credentials);
            
        } catch (\Throwable $e) {
            throw AuthenticationException::tokenExpired();
        }
    }
    
    public function isValid(): bool
    {
        if (empty($this->accessToken)) {
            return false;
        }
        
        if ($this->tokenExpiry === null) {
            // Если нет информации о времени истечения, считаем токен действительным
            // но проверяем, не пора ли его обновить по времени кеширования
            return $this->isTokenFreshEnough();
        }
        
        $now = new \DateTimeImmutable();
        $refreshTime = $this->tokenExpiry->modify("-{$this->refreshThreshold} seconds");
        
        return $now < $refreshTime;
    }
    
    public function authenticate(AuthCredentials $credentials): bool
    {
        if (!$credentials->isValid()) {
            throw AuthenticationException::invalidCredentials();
        }
        
        try {
            // Создаем HTTP клиент для аутентификации
            $client = new \yii\httpclient\Client([
                'baseUrl' => $this->getApiBaseUrl(),
                'contentLoggingMaxSize' => 1000000,
                'responseConfig' => [
                    'format' => \yii\httpclient\Client::FORMAT_JSON
                ],
                'requestConfig' => [
                    'format' => \yii\httpclient\Client::FORMAT_JSON
                ]
            ]);
            
            $response = $client->createRequest()
                ->setMethod('POST')
                ->setUrl($this->authUrl)
                ->setData($credentials->toArray())
                ->send();
            
            if (!$response->isOk) {
                if ($response->getStatusCode() === 401) {
                    throw AuthenticationException::invalidCredentials();
                }
                
                throw AuthenticationException::tokenExpired();
            }
            
            $content = $response->getContent();
            $data = json_decode($content, true);
            
            if (!isset($data['accessToken']) || empty($data['accessToken'])) {
                throw AuthenticationException::tokenExpired();
            }
            
            $this->accessToken = $data['accessToken'];
            $this->tokenExpiry = new \DateTimeImmutable("+{$this->tokenCacheDuration} seconds");
            
            // Сохраняем токен в БД и кеш
            $this->saveTokenToDatabase();
            $this->saveTokenToCache();
            
            return true;
            
        } catch (AuthenticationException $e) {
            throw $e;
        } catch (\Throwable $e) {
            throw AuthenticationException::tokenExpired();
        }
    }
    
    public function clearToken(): void
    {
        $this->accessToken = null;
        $this->tokenExpiry = null;
        
        // Очищаем кеш
        $this->cache->delete(self::TOKEN_CACHE_KEY);
        $this->cache->delete(self::TOKEN_EXPIRY_CACHE_KEY);
    }
    
    public function getTokenExpiry(): ?\DateTimeInterface
    {
        return $this->tokenExpiry;
    }
    
    /**
     * Загружает токен из кеша
     */
    private function loadTokenFromCache(): void
    {
        $this->loadTokenFromDatabase();
    }
    
    /**
     * Загружает токен из БД
     */
    private function loadTokenFromDatabase(): void
    {
        try {
            $token = $this->db->createCommand(
                'SELECT accessToken FROM alientech_api WHERE id = 1'
            )->queryScalar();
            
            if (!empty($token)) {
                $this->accessToken = (string) $token;
            }
        } catch (\Throwable $e) {
            // Игнорируем ошибки загрузки
        }
    }
    
    /**
     * Сохраняет токен в БД
     */
    private function saveTokenToDatabase(): void
    {
        try {
            $this->db->createCommand(
                "INSERT INTO alientech_api (id, accessToken) VALUES(1, :token) 
                 ON DUPLICATE KEY UPDATE accessToken = :token"
            )->bindValues([
                ':token' => $this->accessToken,
            ])->execute();
        } catch (\Throwable $e) {
            // Игнорируем ошибки сохранения
        }
    }
    
    /**
     * Сохраняет токен в кеш
     */
    private function saveTokenToCache(): void
    {
        $this->cache->set(self::TOKEN_CACHE_KEY, $this->accessToken, $this->tokenCacheDuration);
        
        if ($this->tokenExpiry) {
            $this->cache->set(
                self::TOKEN_EXPIRY_CACHE_KEY, 
                $this->tokenExpiry->getTimestamp(), 
                $this->tokenCacheDuration
            );
        }
    }
    
    /**
     * Получает учетные данные из переменных окружения
     */
    private function getCredentialsFromDatabase(): AuthCredentials
    {
        $clientGuid = $_ENV['ALIENTECH_CLIENT_GUID'] ?? '';
        $secretKey = $_ENV['ALIENTECH_SECRET_KEY'] ?? '';
        
        if (empty($clientGuid) || empty($secretKey)) {
            throw AuthenticationException::missingCredentials();
        }
        
        return new AuthCredentials($clientGuid, $secretKey);
    }
    
    /**
     * Получает базовый URL API из переменных окружения
     */
    private function getApiBaseUrl(): string
    {
        return $_ENV['ALIENTECH_API_URL'] ?? 'https://api.alientech.to';
    }
    
    /**
     * Проверяет, достаточно ли свежий токен
     */
    private function isTokenFreshEnough(): bool
    {
        $cacheExpiry = $this->cache->get(self::TOKEN_EXPIRY_CACHE_KEY);
        
        if (!$cacheExpiry) {
            return false;
        }
        
        $now = time();
        $refreshTime = $cacheExpiry - $this->refreshThreshold;
        
        return $now < $refreshTime;
    }
}
