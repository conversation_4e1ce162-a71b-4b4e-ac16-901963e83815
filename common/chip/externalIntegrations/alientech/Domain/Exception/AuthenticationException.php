<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Domain\Exception;

/**
 * Исключение для ошибок аутентификации Alientech API
 */
class AuthenticationException extends ApiException
{
    /**
     * Создает исключение для неверных учетных данных
     */
    public static function invalidCredentials(): self
    {
        return new self('Invalid authentication credentials');
    }
    
    /**
     * Создает исключение для истекшего токена
     */
    public static function tokenExpired(): self
    {
        return new self('Authentication token has expired');
    }
    
    /**
     * Создает исключение для отсутствующих учетных данных
     */
    public static function missingCredentials(): self
    {
        return new self('Authentication credentials are missing');
    }

    /**
     * Создает исключение для неудачной аутентификации
     */
    public static function authenticationFailed(string $reason = ''): self
    {
        $message = 'Authentication failed';
        if (!empty($reason)) {
            $message .= ': ' . $reason;
        }
        return new self($message);
    }
}
