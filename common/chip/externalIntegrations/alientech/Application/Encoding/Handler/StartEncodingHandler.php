<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Application\Encoding\Handler;

use common\chip\event\EventDispatcher;
use common\chip\externalIntegrations\alientech\Application\Encoding\Command\StartEncodingCommand;
use common\chip\externalIntegrations\alientech\Application\Encoding\Event\EncodingStartedEvent;
use common\chip\externalIntegrations\alientech\Domain\Encoding\Entity\EncodingOperation;
use common\chip\externalIntegrations\alientech\Domain\Encoding\Repository\EncodingRepositoryInterface;
use common\chip\externalIntegrations\alientech\Domain\Encoding\Service\EncodingDomainService;
use common\chip\externalIntegrations\alientech\Domain\Shared\FileId;
use common\chip\externalIntegrations\alientech\Domain\Shared\ProjectId;
use common\chip\externalIntegrations\alientech\Infrastructure\ExternalService\AlientechApiClientInterface;

/**
 * Обработчик команды запуска кодирования
 */
final readonly class StartEncodingHandler
{
    public function __construct(
        private EncodingDomainService $domainService,
        private EncodingRepositoryInterface $repository,
        private AlientechApiClientInterface $apiClient,
        private EventDispatcher $eventDispatcher
    ) {
    }

    public function handle(StartEncodingCommand $command): EncodingOperation
    {
        try {
            // Создаем операцию кодирования
            $operation = $this->domainService->createOperation(
                projectId: ProjectId::fromInt($command->projectId),
                fileId: FileId::fromInt($command->fileId),
                filePaths: $command->filePaths,
                callbackUrl: $command->callbackUrl ?: $this->generateCallbackUrl()
            );

            // Сохраняем операцию
            $this->repository->save($operation);

            $userInfo = [
                'projectId' => $command->projectId,
                'fileId' => $command->fileId,
                'operationId' => $operation->getOperationId()->getValue(),
            ];

            // Отправляем запрос в Alientech API
            $externalResponse = $this->apiClient->startEncoding(
                filePaths: $command->filePaths,
                callbackUrl: $operation->getCallbackUrl(),
                userInfo: $userInfo
            );

            // Обновляем операцию с данными от внешнего API
            $operation->start(
                externalOperationId: $externalResponse['guid'] ?? '',
                slotGuid: $externalResponse['slotGUID'] ?? ''
            );

            $operation->setUserInfo($userInfo);

            // Сохраняем обновленную операцию
            $this->repository->save($operation);

            // Отправляем событие о запуске кодирования
            $event = new EncodingStartedEvent(
                operationId: $operation->getOperationId()->getValue(),
                projectId: $command->projectId,
                fileId: $command->fileId,
                externalOperationId: $operation->getExternalOperationId() ?? '',
                slotGuid: $operation->getSlotGuid() ?? ''
            );

            $this->eventDispatcher->dispatch(
                event: $event,
                entityType: 'alientech_encoding',
                entityId: $command->fileId
            );

            return $operation;
        } catch (\Exception $e) {

            throw new \RuntimeException(
                message: "Failed to start encoding operation: {$e->getMessage()}",
                previous: $e
            );
        }
    }

    private function generateCallbackUrl(): string
    {
        // TODO: Заменить на конфигурируемый URL
        return 'http://localhost/api/alientech-encoded';
    }
}
