<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Application\Decoding\Event;

use common\chip\event\core\BaseEvent;

/**
 * Событие успешного завершения декодирования
 */
final class DecodingCompletedEvent extends BaseEvent
{

    const TYPE_OBD = 'OBD';
    const TYPE_BOOT_BENCH = 'BootBench';

    public function __construct(
        private readonly string $operationId,
        private readonly int $projectId,
        private readonly int $fileId,
        private readonly string $externalOperationId,
        private readonly array $result
    ) {
        parent::__construct($operationId, [
            'operation_id' => $operationId,
            'project_id' => $projectId,
            'file_id' => $fileId,
            'external_operation_id' => $externalOperationId,
            'result' => $result,
        ]);
    }

    public function getType(): string
    {
        return 'kess3.decoding.completed';
    }

    public function getDescription(): string
    {
        return sprintf(
            'Decoding completed for project %d, file %d (operation: %s)',
            $this->projectId,
            $this->fileId,
            $this->operationId
        );
    }

    public function getOperationId(): string
    {
        return $this->operationId;
    }

    public function getProjectId(): int
    {
        return $this->projectId;
    }

    public function getFileId(): int
    {
        return $this->fileId;
    }

    public function getExternalOperationId(): string
    {
        return $this->externalOperationId;
    }

    public function getResult(): array
    {
        return $this->result;
    }

    public function isObd(): bool
    {
        return $this->result['kess3Mode'] === self::TYPE_OBD;
    }

    public function isBootBench(): bool
    {
        return $this->result['kess3Mode'] === self::TYPE_BOOT_BENCH;
    }

}
