<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Application\Decoding\Event;

use common\chip\event\core\BaseEvent;

/**
 * Событие ошибки декодирования
 */
final class DecodingFailedEvent extends BaseEvent
{
    public function __construct(
        private readonly string $operationId,
        private readonly int $projectId,
        private readonly int $fileId,
        private readonly string $externalOperationId,
        private readonly array $error,
        private readonly string $errorMessage = ''
    ) {
        parent::__construct($operationId, [
            'operation_id' => $operationId,
            'project_id' => $projectId,
            'file_id' => $fileId,
            'external_operation_id' => $externalOperationId,
            'error' => $error,
            'error_message' => $errorMessage,
        ]);
    }

    public function getType(): string
    {
        return 'kess3.decoding.failed';
    }

    public function getDescription(): string
    {
        return sprintf(
            'Decoding failed for project %d, file %d (operation: %s): %s',
            $this->projectId,
            $this->fileId,
            $this->operationId,
            $this->errorMessage
        );
    }

    public function getOperationId(): string
    {
        return $this->operationId;
    }

    public function getProjectId(): int
    {
        return $this->projectId;
    }

    public function getFileId(): int
    {
        return $this->fileId;
    }

    public function getExternalOperationId(): string
    {
        return $this->externalOperationId;
    }

    public function getError(): array
    {
        return $this->error;
    }

    public function getErrorMessage(): string
    {
        return $this->errorMessage;
    }
}
