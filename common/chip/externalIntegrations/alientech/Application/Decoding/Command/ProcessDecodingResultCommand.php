<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Application\Decoding\Command;

/**
 * Команда для обработки результата декодирования из callback
 */
final readonly class ProcessDecodingResultCommand
{
    public function __construct(
        public string $externalOperationId,
        public bool $isSuccessful,
        public bool $isCompleted,
        public ?array $result = null,
        public ?array $error = null,
        public ?array $additionalInfo = null
    ) {
    }

    public static function fromCallbackData(array $callbackData): self
    {
        return new self(
            externalOperationId: $callbackData['guid'] ?? $callbackData['GUID'] ?? '',
            isSuccessful: $callbackData['isSuccessful'] ?? $callbackData['IsSuccessful'] ?? false,
            isCompleted: $callbackData['isCompleted'] ?? $callbackData['IsCompleted'] ?? false,
            result: $callbackData['result'] ?? $callbackData['Result'] ?? null,
            error: $callbackData['error'] ?? $callbackData['Error'] ?? null,
            additionalInfo: $callbackData['additionalInfo'] ?? $callbackData['AdditionalInfo'] ?? null
        );
    }
}
